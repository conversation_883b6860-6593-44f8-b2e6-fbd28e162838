# Outlook邮件API服务 - Docker环境变量配置示例
# 复制此文件为 .env 并根据需要修改配置

# =============================================================================
# 服务配置
# =============================================================================

# 服务监听地址（默认: 0.0.0.0）
HOST=0.0.0.0

# 服务监听端口（默认: 8000）
PORT=8000

# 工作进程数（默认: 1，建议设置为CPU核心数）
WORKERS=1

# =============================================================================
# Python运行时配置
# =============================================================================

# 禁用Python字节码文件生成（默认: 1）
PYTHONDONTWRITEBYTECODE=1

# 强制Python输出不缓冲（默认: 1）
PYTHONUNBUFFERED=1

# =============================================================================
# 应用配置
# =============================================================================

# 日志级别（可选: DEBUG, INFO, WARNING, ERROR）
LOG_LEVEL=INFO

# 是否启用调试模式（默认: false）
DEBUG=false

# =============================================================================
# IMAP配置（已内置，通常无需修改）
# =============================================================================

# Outlook IMAP服务器（默认: outlook.live.com）
# IMAP_SERVER=outlook.live.com

# IMAP端口（默认: 993）
# IMAP_PORT=993

# =============================================================================
# 使用说明
# =============================================================================

# 1. 复制此文件：cp docker.env.example .env
# 2. 根据需要修改配置值
# 3. 在 docker-compose.yml 中引用：env_file: .env
# 4. 重启Docker服务：docker-compose restart 